# JWT Configuration
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=admin
spring.datasource.password=123
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

utilizar-spring-ai=true

# Spring AI - Vertex AI Gemini Configuration
spring.ai.vertex.ai.gemini.project-id=estudo-organizado-gemini-ai
spring.ai.vertex.ai.gemini.location= us-east1
spring.ai.vertex.ai.gemini.chat.options.model=gemini-2.5-flash
spring.ai.vertex.ai.gemini.chat.options.temperature=0.3
spring.ai.vertex.ai.gemini.chat.options.max-output-tokens=8192
spring.ai.vertex.ai.gemini.chat.options.top-p=0.8
spring.ai.vertex.ai.gemini.chat.options.top-k=40

# Configuracao de credenciais do Google Cloud (usar variavel de ambiente e mais seguro)
# GOOGLE_APPLICATION_CREDENTIALS deve apontar para o arquivo JSON das credenciais